import 'package:flutter/material.dart';
import '../widgets/date_section.dart';
import '../widgets/todays_sentence_card.dart';
import '../widgets/video_section.dart';
import '../widgets/action_buttons.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.orange.shade50,
      appBar: AppBar(
        title: const Text('Daily Spanish'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 날짜 표시
            DateSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            
            // 오늘의 문장 카드
            TodaysSentenceCard(),
            Sized<PERSON>ox(height: 20),
            
            // 유튜브 영상 섹션
            VideoSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 20),
            
            // 액션 버튼들
            ActionButtons(),
          ],
        ),
      ),
    );
  }
}
