1. 프로젝트 개요
   목적:
   AI와 유튜브 API를 활용해 스페인어 생활 문장과 실제 대화 영상을 매일 자동으로 제공하는 앱을 개발한다.
   번역, 상황 설명, 발음 듣기 등 부가 정보를 함께 제공하여 초보자도 쉽고 재미있게 학습할 수 있도록 한다.

2. 주요 타깃
   스페인어를 처음 배우거나, 여행/실생활에서 자주 쓰이는 표현을 익히고 싶은 초급~중급 학습자

하루 1문장, 실전 상황 예문/영상으로 외국어에 꾸준히 노출되고 싶은 사용자

3. 기능 요구사항
   3.1 AI 기반 문장/번역 자동 생성
   매일 1회, OpenAI GPT 등 LLM을 사용해

스페인어 문장 1개와 한국어 번역을 자동 생성

(관리자 페이지에서 문장 직접 입력/수정도 가능)

3.2 유튜브 영상 자동 매칭
AI가 생성한 문장을 기반으로

YouTube Data API를 이용, 관련 영상(드라마, 실전 회화, 애니 등) 자동 검색

적합도(키워드, 조회수, 길이 등 기준)로 영상 1~3개 추출, 대표 영상 선정

영상 썸네일/ID 등 Firestore에 함께 저장

3.3 DB 저장 및 관리
매일 생성된 문장/번역/영상 정보를 Firebase Firestore에 저장

DB 구조 예시:

yaml
복사
편집
Collection: daily_sentences

- Document ID: 2025-07-16 - sentence: "¿Dónde está el baño?" - translation: "화장실이 어디에요?" - videoId: "abcdefg123" - videoThumbnail: "https://img.youtube.com/vi/abcdefg123/0.jpg" - description: "여행 필수, 식당/카페 등에서 자주 사용" - ttsUrl: "(음성파일 경로, 필요시)" - createdByAI: true
  (옵션) 관리자 화면에서 데이터 검수/수정 가능

  3.4 Flutter 앱 주요 화면/기능
  3.4.1 메인(오늘의 문장) 화면
  오늘의 문장(스페인어, 번역) 표시

유튜브 영상 썸네일 (터치 시 인앱/외부로 영상 재생)

상황 설명, 발음 듣기 버튼

즐겨찾기, 공유, 광고 등 부가 기능

3.4.2 즐겨찾기/복습 화면
저장한 문장만 리스트로 제공

3.4.3 지난 문장/다음 문장 보기
날짜별로 과거 문장 확인, 미래 문장 미리보기(옵션)

3.4.4 설정/부가 기능
알림 설정(매일 푸시), 다크모드, 폰트 크기 조절 등

3.5 자동화/운영
(초기 MVP)

파이썬/노드 등 서버리스 스크립트로 하루 1회 AI 문장+영상 자동 생성, Firestore에 등록

(확장)

Google Cloud Functions, Firebase Functions 등으로 배치 자동화

4. 기술 요구사항
   프론트: Flutter (Android/iOS)

백엔드: Firebase (Firestore, Storage, Auth), Cloud Functions

AI: OpenAI API(GPT-4o 등), Google Gemini 등

영상: YouTube Data API

TTS(발음): Google TTS, Azure TTS 등 필요시

광고: Google AdMob

자동화: 서버리스 스크립트(Python/Node.js) or Cloud Functions

5. 비기능 요구사항
   빠른 로딩, 간결한 UX

반응형 UI(폰트 크기/다크모드/접근성)

데이터 암호화, 개인정보 보호

앱스토어/구글플레이 배포 기준 준수

6. 개발/운영 흐름 (요약)
   (매일 0시)

AI로 오늘의 문장+번역 생성

해당 문장으로 유튜브 영상 자동 검색

가장 적합한 영상 선택(썸네일/ID)

Firestore에 저장

(앱 실행)

Firestore에서 오늘 날짜 데이터 읽기

문장/번역/영상/설명 노출

부가 기능(발음듣기, 즐겨찾기, 공유 등) 제공

(관리자/운영)

필요시 데이터 수동 검수/수정

통계 및 관리 대시보드(옵션)

7. 기타 참고
   최초 MVP는 “오늘의 문장 자동화” + “유튜브 영상 자동 매칭” + “앱 메인화면 노출”에 집중

향후, 발음평가, 댓글, 문장추천, 커뮤니티 등 확장 가능

8. 폴더 구조 참고
   lib/
   core/ // 앱 전체에서 사용하는 핵심(테마, 라우트, 상수 등)
   features/ // FSD와 동일하게 각 도메인별 폴더
   shared/ // 공용 위젯, 서비스 등
   main.dart
